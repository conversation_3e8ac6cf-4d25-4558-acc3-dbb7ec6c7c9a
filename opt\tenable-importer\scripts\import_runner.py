# /opt/tenable-importer/scripts/import_runner.py

import json
import os
import shutil
import xml.etree.ElementTree as ET
from xml.dom import minidom
from tenable.sc import TenableSC
import datetime

# --- CONFIGURATION ---
# Tenable.sc Details
SC_HOST = 'your-tenable-sc-host.com'
ACCESS_KEY = 'your_access_key'
SECRET_KEY = 'your_secret_key'
REPO_ID = 1  # The ID of the Tenable.sc repository for these results

# Directory Paths
REPORTS_DIR = '/opt/tenable-importer/reports/'
LOG_FILE = '/opt/tenable-importer/logs/importer.log'

# --- UTILITY FUNCTIONS ---
def log_message(message):
    """Writes a message to the log file with a timestamp."""
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    with open(LOG_FILE, 'a') as f:
        f.write(f"[{timestamp}] {message}\n")

def pretty_print_xml(element):
    """Returns a nicely formatted XML string for the Element Tree."""
    rough_string = ET.tostring(element, 'utf-8')
    reparsed = minidom.parseString(rough_string)
    return reparsed.toprettyxml(indent="  ")

def archive_file(filepath):
    """Moves a processed file to an 'archive' subdirectory."""
    archive_dir = os.path.join(os.path.dirname(filepath), 'archive')
    if not os.path.exists(archive_dir):
        os.makedirs(archive_dir)
    shutil.move(filepath, os.path.join(archive_dir, os.path.basename(filepath)))

# --- CONVERSION LOGIC ---
def map_severity_to_nessus(severity):
    """Maps severity levels to Nessus numerical severity."""
    severity = str(severity).upper()
    severity_mapping = {
        "CRITICAL": "4", "HIGH": "3", "MEDIUM": "2", "LOW": "1"
    }
    return severity_mapping.get(severity, "0")

def create_from_trivy(json_path, report_name):
    """Creates a .nessus XML structure from a Trivy JSON report."""
    with open(json_path, 'r') as f:
        data = json.load(f)
    
    nessus_root = ET.Element("NessusClientData_v2")
    report = ET.SubElement(nessus_root, "Report", name=report_name)
    host_name = data.get("ArtifactName", report_name)
    report_host = ET.SubElement(report, "ReportHost", name=host_name)
    ET.SubElement(ET.SubElement(report_host, "HostProperties"), "tag", name="host-fqdn").text = host_name

    for result in data.get("Results", []):
        for vuln in result.get("Vulnerabilities", []):
            item = ET.SubElement(report_host, "ReportItem", port="0", svc_name="general", protocol="tcp",
                                 severity=map_severity_to_nessus(vuln.get("Severity")),
                                 pluginID="100001", pluginName=vuln.get("VulnerabilityID"), pluginFamily="Trivy Scan")
            item.find("./..").append(ET.Comment(f" Mapped from {vuln.get('PkgName')} ")) # Add component as comment
            ET.SubElement(item, "description").text = vuln.get("Description", "N/A")
            ET.SubElement(item, "solution").text = f"Upgrade {vuln.get('PkgName')} to version {vuln.get('FixedVersion', 'a patched version')}."
            ET.SubElement(item, "synopsis").text = f"Vulnerability {vuln.get('VulnerabilityID')} in {vuln.get('PkgName')}."
            if "PrimaryURL" in vuln: ET.SubElement(item, "see_also").text = vuln["PrimaryURL"]
            
    return pretty_print_xml(nessus_root)

def create_from_xray(json_path, report_name):
    """Creates a .nessus XML structure from a JFrog Xray JSON report."""
    with open(json_path, 'r') as f:
        data = json.load(f)
        
    nessus_root = ET.Element("NessusClientData_v2")
    report = ET.SubElement(nessus_root, "Report", name=report_name)

    for violation in data.get("violations", []):
        component_name = violation.get("component", "unknown-component")
        report_host = report.find(f".//ReportHost[@name='{component_name}']")
        if report_host is None:
            report_host = ET.SubElement(report, "ReportHost", name=component_name)
            ET.SubElement(ET.SubElement(report_host, "HostProperties"), "tag", name="host-fqdn").text = component_name

        for cve in violation.get("cves", []):
            cve_id = cve.get("cve", "N/A")
            # Create a unique-ish plugin ID from the CVE numbers
            plugin_id = "9" + "".join(filter(str.isdigit, cve_id))[-8:] if cve_id != "N/A" else "999999"
            
            item = ET.SubElement(report_host, "ReportItem", port="0", svc_name="general", protocol="tcp",
                                 severity=map_severity_to_nessus(violation.get("severity")),
                                 pluginID=plugin_id, pluginName=cve_id, pluginFamily="JFrog Xray Scan")
            ET.SubElement(item, "description").text = violation.get("summary", "No summary provided.")
            ET.SubElement(item, "solution").text = "See remediation details in the JFrog Platform."
            ET.SubElement(item, "synopsis").text = f"Vulnerability {cve_id} detected in {component_name}."
            if "cvss_v3_score" in cve: ET.SubElement(item, "cvss_v3_base_score").text = str(cve["cvss_v3_score"])
            
    return pretty_print_xml(nessus_root)


# --- TENABLE.SC UPLOAD ---
def upload_to_tenable_sc(nessus_content, filename_prefix):
    """Uploads a .nessus file to Tenable.sc and logs the action."""
    nessus_filename = f"{filename_prefix}.nessus"
    log_message(f"Attempting to upload {nessus_filename} to Tenable.sc...")
    try:
        sc = TenableSC(SC_HOST)
        sc.login(access_key=ACCESS_KEY, secret_key=SECRET_KEY)
        
        with open(nessus_filename, 'w') as f:
            f.write(nessus_content)

        with open(nessus_filename, 'rb') as f_binary:
            sc.scan_results.import_scan(f_binary, repo_id=REPO_ID)
            
        log_message(f"SUCCESS: Uploaded {nessus_filename} to repository ID {REPO_ID}")
        sc.logout()
        os.remove(nessus_filename) # Clean up the temp .nessus file
        return True
    except Exception as e:
        log_message(f"ERROR: Failed to upload to Tenable.sc. Reason: {e}")
        return False

# --- MAIN WORKFLOW ---
def main():
    """Main function to find reports, convert them, and upload them."""
    log_message("Starting importer run...")
    
    # Process Trivy Reports
    trivy_dir = os.path.join(REPORTS_DIR, 'trivy')
    for filename in os.listdir(trivy_dir):
        if filename.endswith('.json'):
            filepath = os.path.join(trivy_dir, filename)
            log_message(f"Processing Trivy report: {filename}")
            report_name = f"Trivy_{os.path.splitext(filename)[0]}"
            nessus_data = create_from_trivy(filepath, report_name)
            if upload_to_tenable_sc(nessus_data, report_name):
                archive_file(filepath)

    # Process Xray Reports
    xray_dir = os.path.join(REPORTS_DIR, 'xray')
    for filename in os.listdir(xray_dir):
        if filename.endswith('.json'):
            filepath = os.path.join(xray_dir, filename)
            log_message(f"Processing Xray report: {filename}")
            report_name = f"Xray_{os.path.splitext(filename)[0]}"
            nessus_data = create_from_xray(filepath, report_name)
            if upload_to_tenable_sc(nessus_data, report_name):
                archive_file(filepath)

    log_message("Importer run finished.")

if __name__ == "__main__":
    main()
