{"total_violations": 2, "violations": [{"severity": "High", "type": "security", "provider": "JFrog", "created": "2023-01-01T10:00:00Z", "watch_name": "security-watch", "issue_id": "XRAY-123456", "summary": "High severity security vulnerability detected in component", "description": "A high severity vulnerability was found that could lead to remote code execution.", "component": "org.apache.logging.log4j:log4j-core:2.14.1", "package_type": "<PERSON><PERSON>", "infected_files": ["log4j-core-2.14.1.jar"], "cves": [{"cve": "CVE-2021-44228", "cvss_v2_score": 10.0, "cvss_v3_score": 10.0, "cvss_v3_vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H"}], "references": ["https://nvd.nist.gov/vuln/detail/CVE-2021-44228", "https://logging.apache.org/log4j/2.x/security.html"]}, {"severity": "Medium", "type": "security", "provider": "JFrog", "created": "2023-01-02T14:30:00Z", "watch_name": "security-watch", "issue_id": "XRAY-789012", "summary": "Medium severity vulnerability in JSON processing library", "description": "A vulnerability in the JSON processing library could allow denial of service attacks.", "component": "com.fasterxml.jackson.core:jackson-databind:2.12.3", "package_type": "<PERSON><PERSON>", "infected_files": ["jackson-databind-2.12.3.jar"], "cves": [{"cve": "CVE-2022-42003", "cvss_v2_score": 5.0, "cvss_v3_score": 7.5, "cvss_v3_vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H"}], "references": ["https://nvd.nist.gov/vuln/detail/CVE-2022-42003", "https://github.com/FasterXML/jackson-databind/issues/3582"]}]}