{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.1", "globals": "fa6951ecea499d81d35f287ede511bda", "files": {"z_139a551a4e3acb07_import_runner_py": {"hash": "bb11cf74423be94fd6afcbc6c5fbd238", "index": {"url": "z_139a551a4e3acb07_import_runner_py.html", "file": "opt\\tenable-importer\\scripts\\import_runner.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 107, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}