# Automated Trivy and J<PERSON>rog Xray Vulnerability Importer for Tenable.sc

This project provides a set of scripts to automatically export vulnerability reports from Trivy and JFrog Xray, convert them to the `.nessus` format, and upload them to Tenable.sc. This allows for centralized viewing and management of container and artifact vulnerabilities within Tenable.sc.

## Table of Contents

- [Objective](#objective)
- [Process Overview](#process-overview)
- [Prerequisites](#prerequisites)
- [File Structure](#file-structure)
- [Configuration](#configuration)
  - [1. Master Importer Script](#1-master-importer-script)
  - [2. <PERSON><PERSON>ripts](#2-scan-scripts)
- [Usage](#usage)
  - [Manual Execution](#manual-execution)
  - [Automated Execution](#automated-execution)
    - [Linux (Cron)](#linux-cron)
    - [Windows (Task-Scheduler)](#windows-task-scheduler)
- [Logging](#logging)

---

## Objective

To create a fully automated system that regularly imports vulnerability data from JFrog Xray and <PERSON>vy scans into Tenable.sc for centralized reporting and analysis.

## Process Overview

1.  **Export:** Scripts automatically run scans and export vulnerability reports from <PERSON>vy and <PERSON><PERSON> into a designated folder.
2.  **Transform & Load:** A master Python script (`import_runner.py`) identifies these reports, converts them into the `.nessus` format, and uploads them to Tenable.sc using its API.
3.  **Automate:** The entire workflow is scheduled to run on a regular basis using a cron job (Linux) or a Scheduled Task (Windows).

## Prerequisites

Ensure the following are in place on the server that will run the automation scripts:

*   **Python 3.6+**: With the `pytenable` library installed.
    ```bash
    pip install pytenable
    ```
*   **API Credentials**:
    *   **Tenable.sc**: API Access Key and Secret Key from a user account with privileges to create scan results.
    *   **JFrog Platform**: A user token or credentials with permissions to access Xray reports via its API.
*   **Trivy**: The Trivy command-line tool must be installed and available in the system's PATH.

## File Structure

The scripts use the following directory structure. This is created automatically if it doesn't exist.

```
/opt/tenable-importer/
|-- scripts/
|   |-- import_runner.py      # The master Python script
|   |-- run_trivy_scan.sh     # Script to run Trivy scans
|   |-- run_xray_scan.sh      # Script to run Xray scans
|-- reports/
|   |-- xray/                 # Xray JSON reports are placed here
|   |   |-- archive/          # Processed reports are moved here
|   |-- trivy/                # Trivy JSON reports are placed here
|   |   |-- archive/          # Processed reports are moved here
|-- logs/
|   |-- importer.log          # Log file for the master script
|-- README.md                 # This file
```

---

## Configuration

Before running the scripts, you must configure them with your specific environment details.

### 1. Master Importer Script

Edit the configuration section at the top of `/opt/tenable-importer/scripts/import_runner.py`:

```python
# --- CONFIGURATION ---
# Tenable.sc Details
SC_HOST = 'your-tenable-sc-host.com'  # Your Tenable.sc hostname
ACCESS_KEY = 'your_access_key'        # Your Tenable.sc API access key
SECRET_KEY = 'your_secret_key'        # Your Tenable.sc API secret key
REPO_ID = 1                           # The ID of the Tenable.sc repository for results

# Directory Paths
REPORTS_DIR = '/opt/tenable-importer/reports/'
LOG_FILE = '/opt/tenable-importer/logs/importer.log'
```

### 2. Scan Scripts

#### Trivy Scan Script

Edit `/opt/tenable-importer/scripts/run_trivy_scan.sh` and replace the placeholder image name with the actual Docker image you want to scan.

```bash
# /opt/tenable-importer/scripts/run_trivy_scan.sh
#!/bin/bash
IMAGE_NAME="your-app:latest" # <-- CHANGE THIS
REPORT_NAME="trivy_report_$(date +%Y%m%d_%H%M).json"
trivy image --format json --output /opt/tenable-importer/reports/trivy/$REPORT_NAME $IMAGE_NAME
```

#### JFrog Xray Scan Script

Edit `/opt/tenable-importer/scripts/run_xray_scan.sh` and update the JFrog instance URL, credentials, and build name.

```bash
# /opt/tenable-importer/scripts/run_xray_scan.sh
#!/bin/bash
REPORT_NAME="xray_report_$(date +%Y%m%d_%H%M).json"

# JFrog API command to export report for a specific build/artifact
curl -u your_user:your_token \ # <-- CHANGE THIS
     -X POST "https://your-jfrog-instance/xray/api/v1/reports/vulnerabilities" \ # <-- CHANGE THIS
     -H "Content-Type: application/json" \
     -d '{"name": "temp-report", "resources": {"builds": {"include_patterns": ["your-build-name/*"]}}, "filters": {}}' \ # <-- CHANGE THIS
     -o /opt/tenable-importer/reports/xray/$REPORT_NAME
```

---

## Usage

### Manual Execution

You can run the scripts manually to test the process.

1.  **Run the scans** to generate reports:
    ```bash
    /opt/tenable-importer/scripts/run_trivy_scan.sh
    /opt/tenable-importer/scripts/run_xray_scan.sh
    ```
2.  **Check the `reports` directory** to ensure JSON files were created.
3.  **Run the master importer script** to process the reports:
    ```bash
    python3 /opt/tenable-importer/scripts/import_runner.py
    ```
4.  **Check the log file** for status messages and verify the upload in Tenable.sc.

### Automated Execution

The recommended approach is to schedule the scripts to run automatically.

#### Linux (Cron)

1.  **Make the shell scripts executable**:
    ```bash
    chmod +x /opt/tenable-importer/scripts/*.sh
    ```
2.  **Open your crontab for editing**:
    ```bash
    crontab -e
    ```
3.  **Add the schedule**. The following example runs the scans at 2:00 AM and 2:05 AM, followed by the importer at 2:30 AM daily.

    ```crontab
    # Run vulnerability scans
    0 2 * * * /opt/tenable-importer/scripts/run_trivy_scan.sh
    5 2 * * * /opt/tenable-importer/scripts/run_xray_scan.sh

    # Run the importer to process the reports
    30 2 * * * /usr/bin/python3 /opt/tenable-importer/scripts/import_runner.py
    ```

#### Windows (Task Scheduler)

1.  **Create Windows-equivalent scripts** (`.bat` or `.ps1`) for the Trivy and Xray scans.
2.  Open **Task Scheduler**.
3.  **Create three separate tasks**:
    *   **Task 1 & 2 (Scans):**
        *   Action: "Start a program".
        *   Program/script: Point to your scan script (e.g., `C:\tenable-importer\scripts\run_trivy_scan.bat`).
        *   Triggers: Schedule them to run at your desired time (e.g., daily at 2:00 AM).
    *   **Task 3 (Importer):**
        *   Action: "Start a program".
        *   Program/script: `C:\Path\To\Your\Python\python.exe`
        *   Add arguments: `C:\tenable-importer\scripts\import_runner.py`
        *   Triggers: Schedule it to run shortly after the scan tasks complete (e.g., daily at 2:30 AM).

---

## Logging

All operations performed by the master script (`import_runner.py`) are logged to `/opt/tenable-importer/logs/importer.log`. Check this file for troubleshooting information, including successful uploads and API errors.
