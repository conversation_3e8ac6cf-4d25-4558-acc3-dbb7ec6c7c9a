#!/usr/bin/env python3
"""
Test runner script for the JFrog-Trivy-Nessus-Integration project.
Provides convenient commands to run different types of tests.
"""

import sys
import subprocess
import argparse


def run_command(cmd):
    """Run a command and return the exit code."""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=False)
    return result.returncode


def run_unit_tests():
    """Run unit tests only."""
    cmd = [sys.executable, "-m", "pytest", "test_import_runner.py", "-v"]
    return run_command(cmd)


def run_integration_tests():
    """Run integration tests only."""
    cmd = [sys.executable, "-m", "pytest", "test_integration.py", "-v", "-m", "integration"]
    return run_command(cmd)


def run_all_tests():
    """Run all tests with coverage report."""
    cmd = [
        sys.executable, "-m", "pytest", 
        "test_import_runner.py", "test_integration.py",
        "-v", "--cov=opt/tenable-importer/scripts",
        "--cov-report=term-missing",
        "--cov-report=html:htmlcov"
    ]
    return run_command(cmd)


def run_quick_tests():
    """Run all tests without coverage for quick feedback."""
    cmd = [
        sys.executable, "-m", "pytest", 
        "test_import_runner.py", "test_integration.py",
        "-v", "--tb=short"
    ]
    return run_command(cmd)


def main():
    parser = argparse.ArgumentParser(description="Test runner for JFrog-Trivy-Nessus-Integration")
    parser.add_argument(
        "test_type", 
        choices=["unit", "integration", "all", "quick"],
        help="Type of tests to run"
    )
    
    args = parser.parse_args()
    
    if args.test_type == "unit":
        exit_code = run_unit_tests()
    elif args.test_type == "integration":
        exit_code = run_integration_tests()
    elif args.test_type == "all":
        exit_code = run_all_tests()
    elif args.test_type == "quick":
        exit_code = run_quick_tests()
    
    if exit_code == 0:
        print("\n✅ All tests passed!")
    else:
        print(f"\n❌ Tests failed with exit code {exit_code}")
    
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
