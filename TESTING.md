# Testing Guide

This document describes the testing setup for the JFrog-Trivy-Nessus-Integration project.

## Overview

The project includes comprehensive unit and integration tests to verify that the vulnerability import functionality works correctly. The tests cover:

- **Unit Tests**: Test individual functions and components in isolation
- **Integration Tests**: Test the complete workflow with sample data
- **Code Coverage**: Measure how much of the code is tested

## Test Structure

```
├── test_import_runner.py      # Unit tests for the main script
├── test_integration.py        # Integration tests with sample data
├── test_data/                 # Sample test data files
│   ├── sample_trivy_report.json
│   └── sample_xray_report.json
├── requirements-test.txt      # Testing dependencies
├── pytest.ini               # Pytest configuration
└── run_tests.py             # Test runner script
```

## Prerequisites

Install the testing dependencies:

```bash
pip install -r requirements-test.txt
```

## Running Tests

### Option 1: Using the Test Runner Script

```bash
# Run all tests with coverage
python run_tests.py all

# Run only unit tests
python run_tests.py unit

# Run only integration tests
python run_tests.py integration

# Run all tests quickly (no coverage)
python run_tests.py quick
```

### Option 2: Using pytest directly

```bash
# Run all tests with coverage
pytest test_import_runner.py test_integration.py -v --cov=opt/tenable-importer/scripts --cov-report=term-missing

# Run only unit tests
pytest test_import_runner.py -v

# Run only integration tests
pytest test_integration.py -v -m integration

# Run specific test class
pytest test_import_runner.py::TestSeverityMapping -v

# Run specific test method
pytest test_import_runner.py::TestSeverityMapping::test_map_severity_to_nessus_critical -v
```

## Test Categories

### Unit Tests (`test_import_runner.py`)

1. **TestSeverityMapping**: Tests the severity level mapping function
2. **TestUtilityFunctions**: Tests logging, XML formatting, and file archiving
3. **TestTrivyConversion**: Tests Trivy JSON to Nessus XML conversion
4. **TestXrayConversion**: Tests Xray JSON to Nessus XML conversion
5. **TestTenableSCUpload**: Tests the Tenable.sc upload functionality (mocked)
6. **TestMainWorkflow**: Tests the main processing workflow

### Integration Tests (`test_integration.py`)

1. **TestIntegrationWithSampleData**: Tests with real sample data files
2. **TestXMLValidation**: Validates that generated XML is well-formed

## Sample Test Data

The `test_data/` directory contains realistic sample reports:

- `sample_trivy_report.json`: A Trivy vulnerability scan report for nginx:1.20
- `sample_xray_report.json`: A JFrog Xray report with Log4j and Jackson vulnerabilities

These files are used in integration tests to ensure the conversion logic works with real-world data.

## Test Coverage

The tests achieve 99% code coverage of the main script. The only uncovered line is the `if __name__ == "__main__":` guard.

Coverage reports are generated in:
- Terminal output (with `--cov-report=term-missing`)
- HTML report in `htmlcov/` directory (with `--cov-report=html:htmlcov`)

## Mocking Strategy

The tests use extensive mocking to:
- Avoid requiring actual Tenable.sc credentials
- Prevent file system modifications during testing
- Control external dependencies and API calls
- Test error conditions and edge cases

Key mocked components:
- `TenableSC` class and its methods
- File system operations (`os.listdir`, `shutil.move`, etc.)
- Logging functions
- Network calls (implicitly through TenableSC mocking)

## Adding New Tests

### For New Functions

1. Add unit tests to the appropriate test class in `test_import_runner.py`
2. Use mocking to isolate the function under test
3. Test both success and failure scenarios
4. Test edge cases and invalid inputs

### For New Features

1. Add integration tests to `test_integration.py`
2. Create sample data files if needed
3. Test the complete workflow end-to-end
4. Validate XML output structure and content

### Test Naming Convention

- Test files: `test_*.py`
- Test classes: `Test*`
- Test methods: `test_*`
- Use descriptive names that explain what is being tested

## Continuous Integration

The test suite is designed to run in CI/CD environments:
- No external dependencies required
- All tests use mocking for external services
- Fast execution (typically under 2 seconds)
- Clear pass/fail indicators

## Troubleshooting

### Common Issues

1. **Import errors**: Ensure the scripts directory is in the Python path
2. **Missing test data**: Check that sample files exist in `test_data/`
3. **Platform-specific path issues**: Tests handle Windows/Unix path differences
4. **Mock recursion**: Avoid mocking functions that are called recursively

### Debug Tips

- Use `-v` flag for verbose output
- Use `--tb=long` for detailed tracebacks
- Run specific tests to isolate issues
- Check the HTML coverage report for uncovered code paths

## Best Practices

1. **Test Isolation**: Each test should be independent and not rely on others
2. **Clear Assertions**: Use descriptive assertion messages
3. **Mock External Dependencies**: Don't rely on external services or files
4. **Test Edge Cases**: Include tests for error conditions and boundary values
5. **Keep Tests Fast**: Use mocking to avoid slow operations
6. **Maintain Test Data**: Keep sample data files realistic and up-to-date
