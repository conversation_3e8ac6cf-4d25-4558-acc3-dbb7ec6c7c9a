#!/bin/bash
REPORT_NAME="xray_report_$(date +%Y%m%d_%H%M).json"
# JFrog API command to export report for a specific build/artifact
curl -u your_user:your_token -X POST "https://your-jfrog-instance/xray/api/v1/reports/vulnerabilities" \
-H "Content-Type: application/json" \
-d '{"name": "temp-report", "resources": {"builds": {"include_patterns": ["your-build-name/*"]}}, "filters": {}}' \
-o /opt/tenable-importer/reports/xray/$REPORT_NAME
