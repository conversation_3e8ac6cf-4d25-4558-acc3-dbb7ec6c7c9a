#!/usr/bin/env python3
"""
Integration tests for the import_runner.py script.
Tests with real sample data files to ensure end-to-end functionality.
"""

import pytest
import json
import os
import tempfile
import shutil
import xml.etree.ElementTree as ET
from unittest.mock import patch, MagicMock, ANY
import sys

# Add the scripts directory to the path so we can import the module
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'opt', 'tenable-importer', 'scripts'))

import import_runner


@pytest.mark.integration
class TestIntegrationWithSampleData:
    """Integration tests using sample data files."""
    
    def test_trivy_sample_data_conversion(self):
        """Test conversion of sample Trivy data to Nessus format."""
        sample_file = os.path.join(os.path.dirname(__file__), 'test_data', 'sample_trivy_report.json')
        
        if not os.path.exists(sample_file):
            pytest.skip("Sample Trivy data file not found")
        
        result = import_runner.create_from_trivy(sample_file, "integration_test_trivy")
        
        # Parse the XML result
        root = ET.fromstring(result)
        
        # Verify basic structure
        assert root.tag == "NessusClientData_v2"
        report = root.find("Report")
        assert report.get("name") == "integration_test_trivy"
        
        # Verify host information
        report_host = report.find("ReportHost")
        assert report_host.get("name") == "nginx:1.20"
        
        # Verify vulnerabilities are present
        report_items = report_host.findall("ReportItem")
        assert len(report_items) == 2  # Should have 2 vulnerabilities from sample data
        
        # Check specific vulnerability details
        cve_items = {item.get("pluginName"): item for item in report_items}
        
        # Verify CVE-2023-1234 (HIGH severity)
        assert "CVE-2023-1234" in cve_items
        high_vuln = cve_items["CVE-2023-1234"]
        assert high_vuln.get("severity") == "3"  # HIGH = 3
        assert high_vuln.get("pluginFamily") == "Trivy Scan"
        
        # Verify CVE-2023-5678 (CRITICAL severity)
        assert "CVE-2023-5678" in cve_items
        critical_vuln = cve_items["CVE-2023-5678"]
        assert critical_vuln.get("severity") == "4"  # CRITICAL = 4
        
        # Verify description and solution are present
        description = critical_vuln.find("description")
        assert description is not None
        assert "buffer overflow" in description.text.lower()
        
        solution = critical_vuln.find("solution")
        assert solution is not None
        assert "nginx" in solution.text.lower()
    
    def test_xray_sample_data_conversion(self):
        """Test conversion of sample Xray data to Nessus format."""
        sample_file = os.path.join(os.path.dirname(__file__), 'test_data', 'sample_xray_report.json')
        
        if not os.path.exists(sample_file):
            pytest.skip("Sample Xray data file not found")
        
        result = import_runner.create_from_xray(sample_file, "integration_test_xray")
        
        # Parse the XML result
        root = ET.fromstring(result)
        
        # Verify basic structure
        assert root.tag == "NessusClientData_v2"
        report = root.find("Report")
        assert report.get("name") == "integration_test_xray"
        
        # Verify multiple components are handled
        report_hosts = report.findall("ReportHost")
        assert len(report_hosts) == 2  # Should have 2 components from sample data
        
        host_names = [host.get("name") for host in report_hosts]
        assert "org.apache.logging.log4j:log4j-core:2.14.1" in host_names
        assert "com.fasterxml.jackson.core:jackson-databind:2.12.3" in host_names
        
        # Check log4j vulnerability (High severity)
        log4j_host = next(host for host in report_hosts if "log4j" in host.get("name"))
        log4j_items = log4j_host.findall("ReportItem")
        assert len(log4j_items) == 1
        
        log4j_item = log4j_items[0]
        assert log4j_item.get("pluginName") == "CVE-2021-44228"
        assert log4j_item.get("severity") == "3"  # High = 3
        assert log4j_item.get("pluginFamily") == "JFrog Xray Scan"
        
        # Verify CVSS score is included
        cvss_score = log4j_item.find("cvss_v3_base_score")
        assert cvss_score is not None
        assert cvss_score.text == "10.0"
        
        # Check jackson vulnerability (Medium severity)
        jackson_host = next(host for host in report_hosts if "jackson" in host.get("name"))
        jackson_items = jackson_host.findall("ReportItem")
        assert len(jackson_items) == 1
        
        jackson_item = jackson_items[0]
        assert jackson_item.get("pluginName") == "CVE-2022-42003"
        assert jackson_item.get("severity") == "2"  # Medium = 2
    
    @patch('import_runner.upload_to_tenable_sc')
    @patch('import_runner.archive_file')
    @patch('import_runner.log_message')
    def test_end_to_end_workflow_with_sample_data(self, mock_log, mock_archive, mock_upload):
        """Test the complete workflow with sample data files."""
        # Create temporary directory structure
        with tempfile.TemporaryDirectory() as temp_dir:
            # Set up directory structure
            reports_dir = os.path.join(temp_dir, 'reports')
            trivy_dir = os.path.join(reports_dir, 'trivy')
            xray_dir = os.path.join(reports_dir, 'xray')
            logs_dir = os.path.join(temp_dir, 'logs')
            
            os.makedirs(trivy_dir)
            os.makedirs(xray_dir)
            os.makedirs(logs_dir)
            
            # Copy sample data files
            sample_trivy = os.path.join(os.path.dirname(__file__), 'test_data', 'sample_trivy_report.json')
            sample_xray = os.path.join(os.path.dirname(__file__), 'test_data', 'sample_xray_report.json')
            
            if os.path.exists(sample_trivy):
                shutil.copy(sample_trivy, os.path.join(trivy_dir, 'test_trivy.json'))
            if os.path.exists(sample_xray):
                shutil.copy(sample_xray, os.path.join(xray_dir, 'test_xray.json'))
            
            # Mock successful uploads
            mock_upload.return_value = True
            
            # Temporarily override the configuration
            original_reports_dir = import_runner.REPORTS_DIR
            original_log_file = import_runner.LOG_FILE
            
            try:
                import_runner.REPORTS_DIR = reports_dir + '/'
                import_runner.LOG_FILE = os.path.join(logs_dir, 'test.log')
                
                # Run the main workflow
                import_runner.main()
                
                # Verify that files were processed
                if os.path.exists(sample_trivy):
                    mock_upload.assert_any_call(
                        ANY,  # nessus_data content
                        'Trivy_test_trivy'
                    )

                if os.path.exists(sample_xray):
                    mock_upload.assert_any_call(
                        ANY,  # nessus_data content
                        'Xray_test_xray'
                    )
                
                # Verify archiving was called for successful uploads
                expected_calls = 0
                if os.path.exists(sample_trivy):
                    expected_calls += 1
                if os.path.exists(sample_xray):
                    expected_calls += 1
                
                assert mock_archive.call_count == expected_calls
                
            finally:
                # Restore original configuration
                import_runner.REPORTS_DIR = original_reports_dir
                import_runner.LOG_FILE = original_log_file


@pytest.mark.integration
class TestXMLValidation:
    """Test that generated XML is valid and well-formed."""
    
    def test_trivy_xml_is_valid(self):
        """Test that Trivy-generated XML is valid."""
        sample_file = os.path.join(os.path.dirname(__file__), 'test_data', 'sample_trivy_report.json')
        
        if not os.path.exists(sample_file):
            pytest.skip("Sample Trivy data file not found")
        
        result = import_runner.create_from_trivy(sample_file, "xml_validation_test")
        
        # Should be able to parse without errors
        root = ET.fromstring(result)
        
        # Verify XML structure is complete
        assert root.tag == "NessusClientData_v2"
        
        # Verify all required elements are present
        report = root.find("Report")
        assert report is not None
        
        report_host = report.find("ReportHost")
        assert report_host is not None
        
        host_properties = report_host.find("HostProperties")
        assert host_properties is not None
        
        # Verify all ReportItems have required attributes
        for item in report_host.findall("ReportItem"):
            assert item.get("port") is not None
            assert item.get("svc_name") is not None
            assert item.get("protocol") is not None
            assert item.get("severity") is not None
            assert item.get("pluginID") is not None
            assert item.get("pluginName") is not None
            assert item.get("pluginFamily") is not None
    
    def test_xray_xml_is_valid(self):
        """Test that Xray-generated XML is valid."""
        sample_file = os.path.join(os.path.dirname(__file__), 'test_data', 'sample_xray_report.json')
        
        if not os.path.exists(sample_file):
            pytest.skip("Sample Xray data file not found")
        
        result = import_runner.create_from_xray(sample_file, "xml_validation_test")
        
        # Should be able to parse without errors
        root = ET.fromstring(result)
        
        # Verify XML structure is complete
        assert root.tag == "NessusClientData_v2"
        
        # Verify all ReportHosts have proper structure
        report = root.find("Report")
        for host in report.findall("ReportHost"):
            assert host.get("name") is not None
            
            host_properties = host.find("HostProperties")
            assert host_properties is not None
            
            # Verify all ReportItems have required attributes
            for item in host.findall("ReportItem"):
                assert item.get("port") is not None
                assert item.get("svc_name") is not None
                assert item.get("protocol") is not None
                assert item.get("severity") is not None
                assert item.get("pluginID") is not None
                assert item.get("pluginName") is not None
                assert item.get("pluginFamily") is not None


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-m", "integration"])
