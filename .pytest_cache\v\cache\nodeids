["test_import_runner.py::TestMainWorkflow::test_main_workflow", "test_import_runner.py::TestSeverityMapping::test_map_severity_to_nessus_critical", "test_import_runner.py::TestSeverityMapping::test_map_severity_to_nessus_high", "test_import_runner.py::TestSeverityMapping::test_map_severity_to_nessus_low", "test_import_runner.py::TestSeverityMapping::test_map_severity_to_nessus_medium", "test_import_runner.py::TestSeverityMapping::test_map_severity_to_nessus_unknown", "test_import_runner.py::TestTenableSCUpload::test_upload_to_tenable_sc_failure", "test_import_runner.py::TestTenableSCUpload::test_upload_to_tenable_sc_success", "test_import_runner.py::TestTrivyConversion::test_create_from_trivy_basic", "test_import_runner.py::TestTrivyConversion::test_create_from_trivy_empty_results", "test_import_runner.py::TestUtilityFunctions::test_archive_file", "test_import_runner.py::TestUtilityFunctions::test_log_message", "test_import_runner.py::TestUtilityFunctions::test_pretty_print_xml", "test_import_runner.py::TestXrayConversion::test_create_from_xray_basic", "test_import_runner.py::TestXrayConversion::test_create_from_xray_multiple_components", "test_integration.py::TestIntegrationWithSampleData::test_end_to_end_workflow_with_sample_data", "test_integration.py::TestIntegrationWithSampleData::test_trivy_sample_data_conversion", "test_integration.py::TestIntegrationWithSampleData::test_xray_sample_data_conversion", "test_integration.py::TestXMLValidation::test_trivy_xml_is_valid", "test_integration.py::TestXMLValidation::test_xray_xml_is_valid"]