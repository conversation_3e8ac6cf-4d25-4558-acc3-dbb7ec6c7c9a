<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for opt\tenable-importer\scripts\import_runner.py: 99%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>opt\tenable-importer\scripts\import_runner.py</b>:
            <span class="pc_cov">99%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">107 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">106<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">1<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="index.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="index.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-07-01 13:59 +0800
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="com"># /opt/tenable-importer/scripts/import_runner.py</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="key">import</span> <span class="nam">json</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="key">import</span> <span class="nam">os</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="key">import</span> <span class="nam">shutil</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="key">import</span> <span class="nam">xml</span><span class="op">.</span><span class="nam">etree</span><span class="op">.</span><span class="nam">ElementTree</span> <span class="key">as</span> <span class="nam">ET</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="key">from</span> <span class="nam">xml</span><span class="op">.</span><span class="nam">dom</span> <span class="key">import</span> <span class="nam">minidom</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">from</span> <span class="nam">tenable</span><span class="op">.</span><span class="nam">sc</span> <span class="key">import</span> <span class="nam">TenableSC</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">import</span> <span class="nam">datetime</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="com"># --- CONFIGURATION ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="com"># Tenable.sc Details</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t"><span class="nam">SC_HOST</span> <span class="op">=</span> <span class="str">'your-tenable-sc-host.com'</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t"><span class="nam">ACCESS_KEY</span> <span class="op">=</span> <span class="str">'your_access_key'</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t"><span class="nam">SECRET_KEY</span> <span class="op">=</span> <span class="str">'your_secret_key'</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t"><span class="nam">REPO_ID</span> <span class="op">=</span> <span class="num">1</span>  <span class="com"># The ID of the Tenable.sc repository for these results</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t"><span class="com"># Directory Paths</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t"><span class="nam">REPORTS_DIR</span> <span class="op">=</span> <span class="str">'/opt/tenable-importer/reports/'</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t"><span class="nam">LOG_FILE</span> <span class="op">=</span> <span class="str">'/opt/tenable-importer/logs/importer.log'</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t"><span class="com"># --- UTILITY FUNCTIONS ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t"><span class="key">def</span> <span class="nam">log_message</span><span class="op">(</span><span class="nam">message</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">    <span class="str">"""Writes a message to the log file with a timestamp."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">    <span class="nam">timestamp</span> <span class="op">=</span> <span class="nam">datetime</span><span class="op">.</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">now</span><span class="op">(</span><span class="op">)</span><span class="op">.</span><span class="nam">strftime</span><span class="op">(</span><span class="str">"%Y-%m-%d %H:%M:%S"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">    <span class="key">with</span> <span class="nam">open</span><span class="op">(</span><span class="nam">LOG_FILE</span><span class="op">,</span> <span class="str">'a'</span><span class="op">)</span> <span class="key">as</span> <span class="nam">f</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">        <span class="nam">f</span><span class="op">.</span><span class="nam">write</span><span class="op">(</span><span class="fst">f"</span><span class="fst">[</span><span class="op">{</span><span class="nam">timestamp</span><span class="op">}</span><span class="fst">] </span><span class="op">{</span><span class="nam">message</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t"><span class="key">def</span> <span class="nam">pretty_print_xml</span><span class="op">(</span><span class="nam">element</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">    <span class="str">"""Returns a nicely formatted XML string for the Element Tree."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">    <span class="nam">rough_string</span> <span class="op">=</span> <span class="nam">ET</span><span class="op">.</span><span class="nam">tostring</span><span class="op">(</span><span class="nam">element</span><span class="op">,</span> <span class="str">'utf-8'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">    <span class="nam">reparsed</span> <span class="op">=</span> <span class="nam">minidom</span><span class="op">.</span><span class="nam">parseString</span><span class="op">(</span><span class="nam">rough_string</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">    <span class="key">return</span> <span class="nam">reparsed</span><span class="op">.</span><span class="nam">toprettyxml</span><span class="op">(</span><span class="nam">indent</span><span class="op">=</span><span class="str">"  "</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t"><span class="key">def</span> <span class="nam">archive_file</span><span class="op">(</span><span class="nam">filepath</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">    <span class="str">"""Moves a processed file to an 'archive' subdirectory."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">    <span class="nam">archive_dir</span> <span class="op">=</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">dirname</span><span class="op">(</span><span class="nam">filepath</span><span class="op">)</span><span class="op">,</span> <span class="str">'archive'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">exists</span><span class="op">(</span><span class="nam">archive_dir</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">        <span class="nam">os</span><span class="op">.</span><span class="nam">makedirs</span><span class="op">(</span><span class="nam">archive_dir</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">    <span class="nam">shutil</span><span class="op">.</span><span class="nam">move</span><span class="op">(</span><span class="nam">filepath</span><span class="op">,</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">archive_dir</span><span class="op">,</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">basename</span><span class="op">(</span><span class="nam">filepath</span><span class="op">)</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t"><span class="com"># --- CONVERSION LOGIC ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t"><span class="key">def</span> <span class="nam">map_severity_to_nessus</span><span class="op">(</span><span class="nam">severity</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">    <span class="str">"""Maps severity levels to Nessus numerical severity."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">    <span class="nam">severity</span> <span class="op">=</span> <span class="nam">str</span><span class="op">(</span><span class="nam">severity</span><span class="op">)</span><span class="op">.</span><span class="nam">upper</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">    <span class="nam">severity_mapping</span> <span class="op">=</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">        <span class="str">"CRITICAL"</span><span class="op">:</span> <span class="str">"4"</span><span class="op">,</span> <span class="str">"HIGH"</span><span class="op">:</span> <span class="str">"3"</span><span class="op">,</span> <span class="str">"MEDIUM"</span><span class="op">:</span> <span class="str">"2"</span><span class="op">,</span> <span class="str">"LOW"</span><span class="op">:</span> <span class="str">"1"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">    <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">    <span class="key">return</span> <span class="nam">severity_mapping</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="nam">severity</span><span class="op">,</span> <span class="str">"0"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t"><span class="key">def</span> <span class="nam">create_from_trivy</span><span class="op">(</span><span class="nam">json_path</span><span class="op">,</span> <span class="nam">report_name</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">    <span class="str">"""Creates a .nessus XML structure from a Trivy JSON report."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">    <span class="key">with</span> <span class="nam">open</span><span class="op">(</span><span class="nam">json_path</span><span class="op">,</span> <span class="str">'r'</span><span class="op">)</span> <span class="key">as</span> <span class="nam">f</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">        <span class="nam">data</span> <span class="op">=</span> <span class="nam">json</span><span class="op">.</span><span class="nam">load</span><span class="op">(</span><span class="nam">f</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">    <span class="nam">nessus_root</span> <span class="op">=</span> <span class="nam">ET</span><span class="op">.</span><span class="nam">Element</span><span class="op">(</span><span class="str">"NessusClientData_v2"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">    <span class="nam">report</span> <span class="op">=</span> <span class="nam">ET</span><span class="op">.</span><span class="nam">SubElement</span><span class="op">(</span><span class="nam">nessus_root</span><span class="op">,</span> <span class="str">"Report"</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="nam">report_name</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">    <span class="nam">host_name</span> <span class="op">=</span> <span class="nam">data</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"ArtifactName"</span><span class="op">,</span> <span class="nam">report_name</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">    <span class="nam">report_host</span> <span class="op">=</span> <span class="nam">ET</span><span class="op">.</span><span class="nam">SubElement</span><span class="op">(</span><span class="nam">report</span><span class="op">,</span> <span class="str">"ReportHost"</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="nam">host_name</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">    <span class="nam">ET</span><span class="op">.</span><span class="nam">SubElement</span><span class="op">(</span><span class="nam">ET</span><span class="op">.</span><span class="nam">SubElement</span><span class="op">(</span><span class="nam">report_host</span><span class="op">,</span> <span class="str">"HostProperties"</span><span class="op">)</span><span class="op">,</span> <span class="str">"tag"</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">"host-fqdn"</span><span class="op">)</span><span class="op">.</span><span class="nam">text</span> <span class="op">=</span> <span class="nam">host_name</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">    <span class="key">for</span> <span class="nam">result</span> <span class="key">in</span> <span class="nam">data</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"Results"</span><span class="op">,</span> <span class="op">[</span><span class="op">]</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">        <span class="key">for</span> <span class="nam">vuln</span> <span class="key">in</span> <span class="nam">result</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"Vulnerabilities"</span><span class="op">,</span> <span class="op">[</span><span class="op">]</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">            <span class="nam">item</span> <span class="op">=</span> <span class="nam">ET</span><span class="op">.</span><span class="nam">SubElement</span><span class="op">(</span><span class="nam">report_host</span><span class="op">,</span> <span class="str">"ReportItem"</span><span class="op">,</span> <span class="nam">port</span><span class="op">=</span><span class="str">"0"</span><span class="op">,</span> <span class="nam">svc_name</span><span class="op">=</span><span class="str">"general"</span><span class="op">,</span> <span class="nam">protocol</span><span class="op">=</span><span class="str">"tcp"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">                                 <span class="nam">severity</span><span class="op">=</span><span class="nam">map_severity_to_nessus</span><span class="op">(</span><span class="nam">vuln</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"Severity"</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">                                 <span class="nam">pluginID</span><span class="op">=</span><span class="str">"100001"</span><span class="op">,</span> <span class="nam">pluginName</span><span class="op">=</span><span class="nam">vuln</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"VulnerabilityID"</span><span class="op">)</span><span class="op">,</span> <span class="nam">pluginFamily</span><span class="op">=</span><span class="str">"Trivy Scan"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">            <span class="com"># Add component as comment to the report host</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">            <span class="nam">report_host</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="nam">ET</span><span class="op">.</span><span class="nam">Comment</span><span class="op">(</span><span class="fst">f"</span><span class="fst"> Mapped from </span><span class="op">{</span><span class="nam">vuln</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'PkgName'</span><span class="op">)</span><span class="op">}</span><span class="fst"> </span><span class="fst">"</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">            <span class="nam">ET</span><span class="op">.</span><span class="nam">SubElement</span><span class="op">(</span><span class="nam">item</span><span class="op">,</span> <span class="str">"description"</span><span class="op">)</span><span class="op">.</span><span class="nam">text</span> <span class="op">=</span> <span class="nam">vuln</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"Description"</span><span class="op">,</span> <span class="str">"N/A"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">            <span class="nam">ET</span><span class="op">.</span><span class="nam">SubElement</span><span class="op">(</span><span class="nam">item</span><span class="op">,</span> <span class="str">"solution"</span><span class="op">)</span><span class="op">.</span><span class="nam">text</span> <span class="op">=</span> <span class="fst">f"</span><span class="fst">Upgrade </span><span class="op">{</span><span class="nam">vuln</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'PkgName'</span><span class="op">)</span><span class="op">}</span><span class="fst"> to version </span><span class="op">{</span><span class="nam">vuln</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'FixedVersion'</span><span class="op">,</span> <span class="str">'a patched version'</span><span class="op">)</span><span class="op">}</span><span class="fst">.</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">            <span class="nam">ET</span><span class="op">.</span><span class="nam">SubElement</span><span class="op">(</span><span class="nam">item</span><span class="op">,</span> <span class="str">"synopsis"</span><span class="op">)</span><span class="op">.</span><span class="nam">text</span> <span class="op">=</span> <span class="fst">f"</span><span class="fst">Vulnerability </span><span class="op">{</span><span class="nam">vuln</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'VulnerabilityID'</span><span class="op">)</span><span class="op">}</span><span class="fst"> in </span><span class="op">{</span><span class="nam">vuln</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'PkgName'</span><span class="op">)</span><span class="op">}</span><span class="fst">.</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">            <span class="key">if</span> <span class="str">"PrimaryURL"</span> <span class="key">in</span> <span class="nam">vuln</span><span class="op">:</span> <span class="nam">ET</span><span class="op">.</span><span class="nam">SubElement</span><span class="op">(</span><span class="nam">item</span><span class="op">,</span> <span class="str">"see_also"</span><span class="op">)</span><span class="op">.</span><span class="nam">text</span> <span class="op">=</span> <span class="nam">vuln</span><span class="op">[</span><span class="str">"PrimaryURL"</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">    <span class="key">return</span> <span class="nam">pretty_print_xml</span><span class="op">(</span><span class="nam">nessus_root</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t"><span class="key">def</span> <span class="nam">create_from_xray</span><span class="op">(</span><span class="nam">json_path</span><span class="op">,</span> <span class="nam">report_name</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">    <span class="str">"""Creates a .nessus XML structure from a JFrog Xray JSON report."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">    <span class="key">with</span> <span class="nam">open</span><span class="op">(</span><span class="nam">json_path</span><span class="op">,</span> <span class="str">'r'</span><span class="op">)</span> <span class="key">as</span> <span class="nam">f</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">        <span class="nam">data</span> <span class="op">=</span> <span class="nam">json</span><span class="op">.</span><span class="nam">load</span><span class="op">(</span><span class="nam">f</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">    <span class="nam">nessus_root</span> <span class="op">=</span> <span class="nam">ET</span><span class="op">.</span><span class="nam">Element</span><span class="op">(</span><span class="str">"NessusClientData_v2"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">    <span class="nam">report</span> <span class="op">=</span> <span class="nam">ET</span><span class="op">.</span><span class="nam">SubElement</span><span class="op">(</span><span class="nam">nessus_root</span><span class="op">,</span> <span class="str">"Report"</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="nam">report_name</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">    <span class="key">for</span> <span class="nam">violation</span> <span class="key">in</span> <span class="nam">data</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"violations"</span><span class="op">,</span> <span class="op">[</span><span class="op">]</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">        <span class="nam">component_name</span> <span class="op">=</span> <span class="nam">violation</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"component"</span><span class="op">,</span> <span class="str">"unknown-component"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">        <span class="nam">report_host</span> <span class="op">=</span> <span class="nam">report</span><span class="op">.</span><span class="nam">find</span><span class="op">(</span><span class="fst">f"</span><span class="fst">.//ReportHost[@name='</span><span class="op">{</span><span class="nam">component_name</span><span class="op">}</span><span class="fst">']</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">        <span class="key">if</span> <span class="nam">report_host</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">            <span class="nam">report_host</span> <span class="op">=</span> <span class="nam">ET</span><span class="op">.</span><span class="nam">SubElement</span><span class="op">(</span><span class="nam">report</span><span class="op">,</span> <span class="str">"ReportHost"</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="nam">component_name</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">            <span class="nam">ET</span><span class="op">.</span><span class="nam">SubElement</span><span class="op">(</span><span class="nam">ET</span><span class="op">.</span><span class="nam">SubElement</span><span class="op">(</span><span class="nam">report_host</span><span class="op">,</span> <span class="str">"HostProperties"</span><span class="op">)</span><span class="op">,</span> <span class="str">"tag"</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">"host-fqdn"</span><span class="op">)</span><span class="op">.</span><span class="nam">text</span> <span class="op">=</span> <span class="nam">component_name</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">        <span class="key">for</span> <span class="nam">cve</span> <span class="key">in</span> <span class="nam">violation</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"cves"</span><span class="op">,</span> <span class="op">[</span><span class="op">]</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">            <span class="nam">cve_id</span> <span class="op">=</span> <span class="nam">cve</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"cve"</span><span class="op">,</span> <span class="str">"N/A"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">            <span class="com"># Create a unique-ish plugin ID from the CVE numbers</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">            <span class="nam">plugin_id</span> <span class="op">=</span> <span class="str">"9"</span> <span class="op">+</span> <span class="str">""</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">filter</span><span class="op">(</span><span class="nam">str</span><span class="op">.</span><span class="nam">isdigit</span><span class="op">,</span> <span class="nam">cve_id</span><span class="op">)</span><span class="op">)</span><span class="op">[</span><span class="op">-</span><span class="num">8</span><span class="op">:</span><span class="op">]</span> <span class="key">if</span> <span class="nam">cve_id</span> <span class="op">!=</span> <span class="str">"N/A"</span> <span class="key">else</span> <span class="str">"999999"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">            <span class="nam">item</span> <span class="op">=</span> <span class="nam">ET</span><span class="op">.</span><span class="nam">SubElement</span><span class="op">(</span><span class="nam">report_host</span><span class="op">,</span> <span class="str">"ReportItem"</span><span class="op">,</span> <span class="nam">port</span><span class="op">=</span><span class="str">"0"</span><span class="op">,</span> <span class="nam">svc_name</span><span class="op">=</span><span class="str">"general"</span><span class="op">,</span> <span class="nam">protocol</span><span class="op">=</span><span class="str">"tcp"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">                                 <span class="nam">severity</span><span class="op">=</span><span class="nam">map_severity_to_nessus</span><span class="op">(</span><span class="nam">violation</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"severity"</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">                                 <span class="nam">pluginID</span><span class="op">=</span><span class="nam">plugin_id</span><span class="op">,</span> <span class="nam">pluginName</span><span class="op">=</span><span class="nam">cve_id</span><span class="op">,</span> <span class="nam">pluginFamily</span><span class="op">=</span><span class="str">"JFrog Xray Scan"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">            <span class="nam">ET</span><span class="op">.</span><span class="nam">SubElement</span><span class="op">(</span><span class="nam">item</span><span class="op">,</span> <span class="str">"description"</span><span class="op">)</span><span class="op">.</span><span class="nam">text</span> <span class="op">=</span> <span class="nam">violation</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"summary"</span><span class="op">,</span> <span class="str">"No summary provided."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">            <span class="nam">ET</span><span class="op">.</span><span class="nam">SubElement</span><span class="op">(</span><span class="nam">item</span><span class="op">,</span> <span class="str">"solution"</span><span class="op">)</span><span class="op">.</span><span class="nam">text</span> <span class="op">=</span> <span class="str">"See remediation details in the JFrog Platform."</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">            <span class="nam">ET</span><span class="op">.</span><span class="nam">SubElement</span><span class="op">(</span><span class="nam">item</span><span class="op">,</span> <span class="str">"synopsis"</span><span class="op">)</span><span class="op">.</span><span class="nam">text</span> <span class="op">=</span> <span class="fst">f"</span><span class="fst">Vulnerability </span><span class="op">{</span><span class="nam">cve_id</span><span class="op">}</span><span class="fst"> detected in </span><span class="op">{</span><span class="nam">component_name</span><span class="op">}</span><span class="fst">.</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">            <span class="key">if</span> <span class="str">"cvss_v3_score"</span> <span class="key">in</span> <span class="nam">cve</span><span class="op">:</span> <span class="nam">ET</span><span class="op">.</span><span class="nam">SubElement</span><span class="op">(</span><span class="nam">item</span><span class="op">,</span> <span class="str">"cvss_v3_base_score"</span><span class="op">)</span><span class="op">.</span><span class="nam">text</span> <span class="op">=</span> <span class="nam">str</span><span class="op">(</span><span class="nam">cve</span><span class="op">[</span><span class="str">"cvss_v3_score"</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">    <span class="key">return</span> <span class="nam">pretty_print_xml</span><span class="op">(</span><span class="nam">nessus_root</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t"><span class="com"># --- TENABLE.SC UPLOAD ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t"><span class="key">def</span> <span class="nam">upload_to_tenable_sc</span><span class="op">(</span><span class="nam">nessus_content</span><span class="op">,</span> <span class="nam">filename_prefix</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">    <span class="str">"""Uploads a .nessus file to Tenable.sc and logs the action."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">    <span class="nam">nessus_filename</span> <span class="op">=</span> <span class="fst">f"</span><span class="op">{</span><span class="nam">filename_prefix</span><span class="op">}</span><span class="fst">.nessus</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">    <span class="nam">log_message</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Attempting to upload </span><span class="op">{</span><span class="nam">nessus_filename</span><span class="op">}</span><span class="fst"> to Tenable.sc...</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">        <span class="nam">sc</span> <span class="op">=</span> <span class="nam">TenableSC</span><span class="op">(</span><span class="nam">SC_HOST</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">        <span class="nam">sc</span><span class="op">.</span><span class="nam">login</span><span class="op">(</span><span class="nam">access_key</span><span class="op">=</span><span class="nam">ACCESS_KEY</span><span class="op">,</span> <span class="nam">secret_key</span><span class="op">=</span><span class="nam">SECRET_KEY</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">        <span class="key">with</span> <span class="nam">open</span><span class="op">(</span><span class="nam">nessus_filename</span><span class="op">,</span> <span class="str">'w'</span><span class="op">)</span> <span class="key">as</span> <span class="nam">f</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">            <span class="nam">f</span><span class="op">.</span><span class="nam">write</span><span class="op">(</span><span class="nam">nessus_content</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">        <span class="key">with</span> <span class="nam">open</span><span class="op">(</span><span class="nam">nessus_filename</span><span class="op">,</span> <span class="str">'rb'</span><span class="op">)</span> <span class="key">as</span> <span class="nam">f_binary</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">            <span class="nam">sc</span><span class="op">.</span><span class="nam">scan_results</span><span class="op">.</span><span class="nam">import_scan</span><span class="op">(</span><span class="nam">f_binary</span><span class="op">,</span> <span class="nam">repo_id</span><span class="op">=</span><span class="nam">REPO_ID</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t">        <span class="nam">log_message</span><span class="op">(</span><span class="fst">f"</span><span class="fst">SUCCESS: Uploaded </span><span class="op">{</span><span class="nam">nessus_filename</span><span class="op">}</span><span class="fst"> to repository ID </span><span class="op">{</span><span class="nam">REPO_ID</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">        <span class="nam">sc</span><span class="op">.</span><span class="nam">logout</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t">        <span class="nam">os</span><span class="op">.</span><span class="nam">remove</span><span class="op">(</span><span class="nam">nessus_filename</span><span class="op">)</span> <span class="com"># Clean up the temp .nessus file</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t">        <span class="key">return</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t">        <span class="nam">log_message</span><span class="op">(</span><span class="fst">f"</span><span class="fst">ERROR: Failed to upload to Tenable.sc. Reason: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t">        <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t"><span class="com"># --- MAIN WORKFLOW ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t"><span class="key">def</span> <span class="nam">main</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t">    <span class="str">"""Main function to find reports, convert them, and upload them."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t">    <span class="nam">log_message</span><span class="op">(</span><span class="str">"Starting importer run..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t">    <span class="com"># Process Trivy Reports</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t">    <span class="nam">trivy_dir</span> <span class="op">=</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">REPORTS_DIR</span><span class="op">,</span> <span class="str">'trivy'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t">    <span class="key">for</span> <span class="nam">filename</span> <span class="key">in</span> <span class="nam">os</span><span class="op">.</span><span class="nam">listdir</span><span class="op">(</span><span class="nam">trivy_dir</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t">        <span class="key">if</span> <span class="nam">filename</span><span class="op">.</span><span class="nam">endswith</span><span class="op">(</span><span class="str">'.json'</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t">            <span class="nam">filepath</span> <span class="op">=</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">trivy_dir</span><span class="op">,</span> <span class="nam">filename</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t">            <span class="nam">log_message</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Processing Trivy report: </span><span class="op">{</span><span class="nam">filename</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">            <span class="nam">report_name</span> <span class="op">=</span> <span class="fst">f"</span><span class="fst">Trivy_</span><span class="op">{</span><span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">splitext</span><span class="op">(</span><span class="nam">filename</span><span class="op">)</span><span class="op">[</span><span class="num">0</span><span class="op">]</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t">            <span class="nam">nessus_data</span> <span class="op">=</span> <span class="nam">create_from_trivy</span><span class="op">(</span><span class="nam">filepath</span><span class="op">,</span> <span class="nam">report_name</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t">            <span class="key">if</span> <span class="nam">upload_to_tenable_sc</span><span class="op">(</span><span class="nam">nessus_data</span><span class="op">,</span> <span class="nam">report_name</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">                <span class="nam">archive_file</span><span class="op">(</span><span class="nam">filepath</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t">    <span class="com"># Process Xray Reports</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">    <span class="nam">xray_dir</span> <span class="op">=</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">REPORTS_DIR</span><span class="op">,</span> <span class="str">'xray'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t">    <span class="key">for</span> <span class="nam">filename</span> <span class="key">in</span> <span class="nam">os</span><span class="op">.</span><span class="nam">listdir</span><span class="op">(</span><span class="nam">xray_dir</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t">        <span class="key">if</span> <span class="nam">filename</span><span class="op">.</span><span class="nam">endswith</span><span class="op">(</span><span class="str">'.json'</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">            <span class="nam">filepath</span> <span class="op">=</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">xray_dir</span><span class="op">,</span> <span class="nam">filename</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t">            <span class="nam">log_message</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Processing Xray report: </span><span class="op">{</span><span class="nam">filename</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t">            <span class="nam">report_name</span> <span class="op">=</span> <span class="fst">f"</span><span class="fst">Xray_</span><span class="op">{</span><span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">splitext</span><span class="op">(</span><span class="nam">filename</span><span class="op">)</span><span class="op">[</span><span class="num">0</span><span class="op">]</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">            <span class="nam">nessus_data</span> <span class="op">=</span> <span class="nam">create_from_xray</span><span class="op">(</span><span class="nam">filepath</span><span class="op">,</span> <span class="nam">report_name</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t">            <span class="key">if</span> <span class="nam">upload_to_tenable_sc</span><span class="op">(</span><span class="nam">nessus_data</span><span class="op">,</span> <span class="nam">report_name</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t">                <span class="nam">archive_file</span><span class="op">(</span><span class="nam">filepath</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t">    <span class="nam">log_message</span><span class="op">(</span><span class="str">"Importer run finished."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t159" href="#t159">159</a></span><span class="t"><span class="key">if</span> <span class="nam">__name__</span> <span class="op">==</span> <span class="str">"__main__"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t160" href="#t160">160</a></span><span class="t">    <span class="nam">main</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="index.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-07-01 13:59 +0800
        </p>
    </div>
</footer>
</body>
</html>
